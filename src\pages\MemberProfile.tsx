import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { 
  User, 
  Calendar, 
  Ticket, 
  DollarSign, 
  FileText, 
  Gift, 
  Settings, 
  CalendarClock,
  CreditCard,
  ChevronRight
} from "lucide-react";
import { useNavigate } from "react-router-dom";

const MemberProfile = () => {
  const navigate = useNavigate();

  // Mock user data
  const user = {
    name: "王小明",
    avatar: "",
    bookings: 0,
    coupons: 1,
    balance: 0,
    surveys: 2,
    rewards: 0
  };

  const menuItems = [
    {
      icon: DollarSign,
      title: "目前儲值金",
      value: user.balance,
      color: "text-yellow-600",
      bgColor: "bg-yellow-50"
    },
    {
      icon: FileText,
      title: "問券與同意書",
      value: user.surveys,
      color: "text-blue-600",
      bgColor: "bg-blue-50"
    },
    {
      icon: Gift,
      title: "紅利兌換",
      value: user.rewards,
      color: "text-red-600",
      bgColor: "bg-red-50"
    },
    {
      icon: User,
      title: "個人設定",
      value: null,
      color: "text-cyan-600",
      bgColor: "bg-cyan-50"
    },
    {
      icon: CalendarClock,
      title: "預約管理",
      value: null,
      color: "text-green-600",
      bgColor: "bg-green-50"
    },
    {
      icon: CreditCard,
      title: "票券管理",
      value: null,
      color: "text-orange-600",
      bgColor: "bg-orange-50"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-gradient-to-b from-primary/10 to-background p-6 text-center">
        <h1 className="text-lg font-semibold mb-6">HOTCAKE | Powered by 夯客</h1>
        
        {/* User Profile */}
        <div className="space-y-4">
          <div className="relative">
            <Avatar className="w-24 h-24 mx-auto border-4 border-white shadow-lg">
              <AvatarFallback className="bg-muted text-2xl font-semibold">
                {user.name.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <div className="absolute bottom-0 right-1/2 transform translate-x-6 translate-y-1 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-md">
              <User className="w-4 h-4 text-muted-foreground" />
            </div>
          </div>
          
          <h2 className="text-xl font-semibold text-foreground">{user.name}</h2>
          
          <Button 
            className="beauty-gradient text-white px-8 py-3 rounded-full font-medium"
            onClick={() => navigate("/services")}
          >
            我要預約
          </Button>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-2 gap-4">
          <Card className="p-6 text-center card-shadow">
            <div className="text-3xl font-bold text-foreground mb-1">{user.bookings}</div>
            <div className="text-sm text-muted-foreground">我的預約</div>
          </Card>
          <Card className="p-6 text-center card-shadow">
            <div className="text-3xl font-bold text-primary mb-1">{user.coupons}</div>
            <div className="text-sm text-muted-foreground">擁有票券</div>
          </Card>
        </div>

        {/* Menu Items */}
        <div className="space-y-3">
          {menuItems.map((item, index) => (
            <Card 
              key={index}
              className="p-4 cursor-pointer smooth-transition hover:shadow-md card-shadow"
              onClick={() => {
                const navigationMap = {
                  "目前儲值金": "/wallet",
                  "問券與同意書": "/business-survey", 
                  "紅利兌換": "/rewards",
                  "個人設定": "/settings",
                  "預約管理": "/booking-management",
                  "票券管理": "/coupons"
                };
                const path = navigationMap[item.title as keyof typeof navigationMap];
                if (path) navigate(path);
              }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className={`w-10 h-10 ${item.bgColor} rounded-lg flex items-center justify-center`}>
                    <item.icon className={`w-5 h-5 ${item.color}`} />
                  </div>
                  <span className="font-medium text-foreground">{item.title}</span>
                </div>
                <div className="flex items-center space-x-2">
                  {item.value !== null && (
                    <Badge variant="secondary" className="text-sm">
                      {item.value}
                    </Badge>
                  )}
                  <ChevronRight className="w-5 h-5 text-muted-foreground" />
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* Footer */}
      <div className="text-center py-8 space-y-1">
        <p className="text-xs text-muted-foreground">Powered by 夯客</p>
        <p className="text-xs text-muted-foreground">v3.11.0</p>
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-background border-t border-border">
        <div className="grid grid-cols-2">
          <Button 
            variant="ghost" 
            className="h-16 flex flex-col space-y-1 rounded-none"
            onClick={() => navigate("/services")}
          >
            <Calendar className="w-5 h-5" />
            <span className="text-xs">我要預約</span>
          </Button>
          <Button 
            variant="ghost" 
            className="h-16 flex flex-col space-y-1 rounded-none bg-accent"
          >
            <Settings className="w-5 h-5" />
            <span className="text-xs">會員資訊</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default MemberProfile;