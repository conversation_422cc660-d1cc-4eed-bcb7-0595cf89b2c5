/**
 * LINE 用戶處理工具
 * 處理 LINE 用戶的虛擬 email 生成和用戶管理
 */

/**
 * 使用瀏覽器原生 Web Crypto API 生成 hash
 */
const generateHash = async (input: string): Promise<string> => {
  const encoder = new TextEncoder();
  const data = encoder.encode(input);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
};

/**
 * 同步版本的 hash 生成（使用簡單的 hash 算法）
 */
const generateSimpleHash = (input: string): string => {
  let hash = 0;
  for (let i = 0; i < input.length; i++) {
    const char = input.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(16).padStart(8, '0');
};

/**
 * 生成有效的虛擬 email 地址
 * 解決 Supabase 不接受 .local 域名的問題
 */
export const generateValidVirtualEmail = (lineUserId: string): string => {
  // 使用簡單 hash 來縮短和標準化 LINE User ID
  const hash = generateSimpleHash(lineUserId);

  // 取前 12 個字符，確保不會太長
  const shortHash = hash.substring(0, 12);

  // 使用有效的域名格式
  return `line.${shortHash}@lineauth.app`;
};

/**
 * 生成安全的虛擬密碼
 */
export const generateVirtualPassword = (lineUserId: string): string => {
  // 使用 LINE User ID 和當前時間戳生成一致但安全的密碼
  const timestamp = Math.floor(Date.now() / 1000); // 使用秒級時間戳
  const hash = generateSimpleHash(`${lineUserId}_${timestamp}`);

  // 生成更長的密碼
  const extendedHash = generateSimpleHash(`${hash}_${lineUserId}`);
  return `LINE_${hash}${extendedHash}`.substring(0, 40);
};

/**
 * 生成一致的虛擬密碼（用於登入）
 * 這個密碼對於同一個 LINE User ID 總是相同的
 */
export const generateConsistentVirtualPassword = (lineUserId: string): string => {
  // 使用固定的鹽值來生成一致的密碼
  const salt = 'LINE_AUTH_SALT_2024';
  const hash = generateSimpleHash(`${lineUserId}_${salt}`);
  const extendedHash = generateSimpleHash(`${salt}_${lineUserId}`);

  return `LINE_${hash}${extendedHash}`.substring(0, 40);
};

/**
 * 驗證 email 格式是否有效
 */
export const validateEmailFormat = (email: string): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  // 基本格式檢查
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    errors.push('Email 格式無效');
  }
  
  // 長度檢查
  if (email.length > 254) {
    errors.push('Email 長度過長（最大 254 字符）');
  }
  
  // 用戶名部分檢查
  const [localPart] = email.split('@');
  if (localPart.length > 64) {
    errors.push('Email 用戶名部分過長（最大 64 字符）');
  }
  
  // 檢查是否包含無效字符（點號是允許的）
  const invalidChars = /[<>()[\]\\,;:\s@"]/;
  if (invalidChars.test(localPart)) {
    errors.push('Email 包含無效字符');
  }
  
  // 檢查域名部分
  const [, domain] = email.split('@');
  if (domain.endsWith('.local') || domain.endsWith('.test')) {
    errors.push('不支援的域名後綴');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};

/**
 * 創建 LINE 用戶的 metadata
 */
export const createLineUserMetadata = (lineProfile: any) => {
  return {
    line_user_id: lineProfile.userId,
    display_name: lineProfile.displayName,
    picture_url: lineProfile.pictureUrl,
    status_message: lineProfile.statusMessage,
    provider: 'line',
    created_via: 'line_login',
    last_login: new Date().toISOString()
  };
};

/**
 * 檢查 LINE User ID 是否有效
 */
export const validateLineUserId = (userId: string): boolean => {
  // LINE User ID 通常是 32-33 個字符的字母數字字符串
  const lineUserIdRegex = /^[a-zA-Z0-9]{32,33}$/;
  return lineUserIdRegex.test(userId);
};

/**
 * 生成用戶顯示名稱
 */
export const generateDisplayName = (lineProfile: any): string => {
  if (lineProfile.displayName) {
    return lineProfile.displayName;
  }
  
  // 如果沒有顯示名稱，使用 LINE 用戶的簡短 ID
  const shortId = lineProfile.userId.substring(0, 8);
  return `LINE用戶_${shortId}`;
};

/**
 * 檢查是否為 LINE 虛擬 email
 */
export const isLineVirtualEmail = (email: string): boolean => {
  return email.startsWith('line.') && email.endsWith('@lineauth.app');
};

/**
 * 從虛擬 email 提取 LINE User ID hash
 */
export const extractLineUserHash = (email: string): string | null => {
  if (!isLineVirtualEmail(email)) {
    return null;
  }
  
  const match = email.match(/^line\.([a-f0-9]{16})@lineauth\.app$/);
  return match ? match[1] : null;
};

/**
 * 生成用戶的唯一標識符
 */
export const generateUserIdentifier = (lineUserId: string): string => {
  const hash = generateSimpleHash(lineUserId);
  return `line_${hash.substring(0, 12)}`;
};

/**
 * 驗證 LINE 登入資料的完整性
 */
export const validateLineLoginData = (tokenData: any): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (!tokenData) {
    errors.push('Token 資料為空');
    return { valid: false, errors };
  }
  
  if (!tokenData.profile) {
    errors.push('缺少用戶資料');
  } else {
    if (!tokenData.profile.userId) {
      errors.push('缺少 LINE User ID');
    } else if (!validateLineUserId(tokenData.profile.userId)) {
      errors.push('LINE User ID 格式無效');
    }
    
    if (!tokenData.profile.displayName) {
      errors.push('缺少顯示名稱');
    }
  }
  
  if (!tokenData.access_token) {
    errors.push('缺少存取權杖');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};

/**
 * 記錄 LINE 登入事件
 */
export const logLineLoginEvent = (lineUserId: string, event: string, details?: any): void => {
  console.group(`🔐 LINE Login Event: ${event}`);
  console.log('LINE User ID:', lineUserId.substring(0, 8) + '...');
  console.log('時間:', new Date().toISOString());
  if (details) {
    console.log('詳細資訊:', details);
  }
  console.groupEnd();
};
