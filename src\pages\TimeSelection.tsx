import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Calendar } from "@/components/ui/calendar";
import { ArrowLeft, ChevronLeft, ChevronRight } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { format, addDays } from "date-fns";
import { zhCN } from "date-fns/locale";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "@/hooks/use-toast";

interface TimeSlot {
  id: string;
  start_time: string;
  duration_minutes: number;
  is_active: boolean;
}

const TimeSelection = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedTime, setSelectedTime] = useState<string>("");
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [bookedSlots, setBookedSlots] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedServices, setSelectedServices] = useState<string[]>([]);

  // 檢查登入狀態和載入已選服務
  useEffect(() => {
    if (!user) {
      toast({
        title: "請先登入",
        description: "您需要登入才能進行預約",
        variant: "destructive",
      });
      navigate("/login");
      return;
    }

    // 載入已選擇的服務
    const storedServices = localStorage.getItem('selectedServices');
    if (!storedServices) {
      toast({
        title: "請先選擇服務",
        description: "請返回選擇您要預約的服務項目",
        variant: "destructive",
      });
      navigate("/services");
      return;
    }

    setSelectedServices(JSON.parse(storedServices));
  }, [user, navigate]);

  // 載入時間段設定
  useEffect(() => {
    const fetchTimeSlots = async () => {
      try {
        const { data, error } = await supabase
          .from('time_slots')
          .select('*')
          .eq('shop_id', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa')
          .eq('is_active', true)
          .order('start_time');

        if (error) throw error;

        console.log('載入的時段資料:', data);
        setTimeSlots(data || []);
      } catch (error: any) {
        console.error('載入時段錯誤:', error);
        toast({
          title: "載入時段失敗",
          description: error.message,
          variant: "destructive",
        });
      }
    };

    fetchTimeSlots();
  }, []);

  // 載入已預約時段
  useEffect(() => {
    const fetchBookedSlots = async () => {
      if (!selectedDate) return;

      setLoading(true);
      try {
        const dateStr = format(selectedDate, 'yyyy-MM-dd');
        const { data, error } = await supabase
          .from('bookings')
          .select('booking_time')
          .eq('booking_date', dateStr)
          .eq('shop_id', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa')
          .in('status', ['pending', 'confirmed']);

        if (error) throw error;

        setBookedSlots(data?.map(booking => booking.booking_time) || []);
      } catch (error: any) {
        toast({
          title: "載入預約狀況失敗",
          description: error.message,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchBookedSlots();
  }, [selectedDate]);

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      // 不允許選擇過去的日期
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (date < today) {
        toast({
          title: "無法選擇過去日期",
          description: "請選擇今天或未來的日期",
          variant: "destructive",
        });
        return;
      }

      setSelectedDate(date);
      setSelectedTime(""); // 重設選擇的時間
    }
  };

  const handleTimeSelect = (time: string) => {
    const formattedTime = formatTimeDisplay(time);
    setSelectedTime(formattedTime);
  };

  const handleNext = () => {
    if (selectedDate && selectedTime) {
      // 將選擇的日期時間存儲到 localStorage
      localStorage.setItem('selectedDate', format(selectedDate, 'yyyy-MM-dd'));
      localStorage.setItem('selectedTime', selectedTime);
      navigate("/booking-confirm");
    }
  };

  const formatSelectedDate = (date: Date) => {
    return format(date, "MM/dd", { locale: zhCN });
  };

  // 格式化時間顯示（移除秒數）
  const formatTimeDisplay = (time: string) => {
    return time.substring(0, 5); // "09:00:00" -> "09:00"
  };

  const isTimeSlotAvailable = (timeSlot: TimeSlot) => {
    // 檢查是否被預約
    const formattedSlotTime = formatTimeDisplay(timeSlot.start_time);
    if (bookedSlots.includes(timeSlot.start_time) || bookedSlots.includes(formattedSlotTime)) {
      return false;
    }

    // 檢查是否為過去時間（如果是今天）
    const today = new Date();
    if (format(selectedDate, 'yyyy-MM-dd') === format(today, 'yyyy-MM-dd')) {
      const currentTime = format(today, 'HH:mm');
      if (formattedSlotTime <= currentTime) {
        return false;
      }
    }

    return true;
  };

  if (!user || selectedServices.length === 0) {
    return null; // 避免在導向過程中閃爍
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="sticky top-0 bg-background/95 backdrop-blur-sm border-b border-border z-10">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center space-x-3">
            <Button 
              variant="ghost" 
              size="icon"
              onClick={() => navigate(-1)}
              className="rounded-full"
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>
            <div>
              <p className="text-sm text-muted-foreground">取消預約</p>
            </div>
          </div>
          <h1 className="font-semibold">Hello 歡迎預約</h1>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Date Selection */}
        <div>
          <h2 className="text-lg font-medium mb-4">請選擇日期與時間</h2>
          
          <Card className="p-4 card-shadow">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-medium">{format(new Date(), 'M')} 月</h3>
              <div className="flex items-center space-x-4">
                <span className="text-sm text-primary">今天</span>
                <div className="flex items-center space-x-2">
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-8 w-8"
                    onClick={() => handleDateSelect(addDays(selectedDate, -7))}
                  >
                    <ChevronLeft className="w-4 h-4" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-8 w-8"
                    onClick={() => handleDateSelect(addDays(selectedDate, 7))}
                  >
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={handleDateSelect}
              locale={zhCN}
              disabled={(date) => {
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                return date < today;
              }}
              className="w-full pointer-events-auto"
              classNames={{
                day_selected: "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
                day_today: "bg-accent text-accent-foreground",
                day_disabled: "text-muted-foreground opacity-50",
              }}
            />
          </Card>
        </div>

        {/* Time Selection */}
        <div>
          <h3 className="font-medium mb-4">
            {formatSelectedDate(selectedDate)} 可預約時間
          </h3>
          
          {loading ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">載入中...</p>
            </div>
          ) : (
            <div className="grid grid-cols-3 gap-3">
              {timeSlots.map((slot) => {
                const available = isTimeSlotAvailable(slot);
                const displayTime = formatTimeDisplay(slot.start_time);
                return (
                  <Button
                    key={slot.id}
                    variant={selectedTime === displayTime ? "default" : "outline"}
                    disabled={!available}
                    onClick={() => available && handleTimeSelect(slot.start_time)}
                    className={`h-12 text-base ${
                      selectedTime === displayTime
                        ? "beauty-gradient text-white"
                        : available
                        ? "hover:bg-accent"
                        : "opacity-50 cursor-not-allowed bg-muted"
                    }`}
                  >
                    {displayTime}
                  </Button>
                );
              })}
            </div>
          )}
        </div>

        {/* Selected Time Summary */}
        {selectedTime && (
          <Card className="p-4 bg-primary-lighter border-primary/20">
            <div className="text-center">
              <p className="text-sm text-muted-foreground mb-1">已選擇時間</p>
              <p className="font-medium text-primary">
                {format(selectedDate, "yyyy/MM/dd (EEEE)", { locale: zhCN })} {selectedTime}
              </p>
            </div>
          </Card>
        )}
      </div>

      {/* Bottom Action */}
      <div className="fixed bottom-0 left-0 right-0 bg-background border-t border-border p-4">
        <Button 
          className="w-full h-12 beauty-gradient text-white rounded-full font-medium smooth-transition disabled:opacity-50"
          onClick={handleNext}
          disabled={!selectedDate || !selectedTime}
        >
          <ArrowLeft className="w-4 h-4 mr-2 rotate-180" />
          填寫預約備註
          <ArrowLeft className="w-4 h-4 ml-2 rotate-180" />
        </Button>
      </div>
    </div>
  );
};

export default TimeSelection;